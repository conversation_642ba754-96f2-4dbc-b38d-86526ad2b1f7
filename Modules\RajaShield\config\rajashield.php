<?php

return [
    'name' => 'RajaShield',

    /*
    |--------------------------------------------------------------------------
    | Permission System Type
    |--------------------------------------------------------------------------
    |
    | Choose the permission system type:
    | 'route' - Route-based permissions
    | 'model' - Model-based permissions  
    | 'dual' - Both route and model permissions
    |
    */
    'permission_system' => env('RAJASHIELD_PERMISSION_SYSTEM', 'dual'),

    /*
    |--------------------------------------------------------------------------
    | Super Admin Role
    |--------------------------------------------------------------------------
    |
    | The name of the super admin role that bypasses all permission checks
    |
    */
    'super_admin_role' => 'super-admin',

    /*
    |--------------------------------------------------------------------------
    | Auto Discovery
    |--------------------------------------------------------------------------
    |
    | Enable auto-discovery of models, resources, and routes for permission generation
    |
    */
    'auto_discovery' => [
        'enabled' => env('RAJASHIELD_AUTO_DISCOVERY', true),
        'models' => true,
        'routes' => true,
        'resources' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Cache settings for permission checks to improve performance
    |
    */
    'cache' => [
        'enabled' => env('RAJASHIELD_CACHE_ENABLED', true),
        'duration' => env('RAJASHIELD_CACHE_DURATION', 300), // 5 minutes
        'prefix' => 'rajashield_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Model Permissions
    |--------------------------------------------------------------------------
    |
    | Standard CRUD permissions that will be generated for each model
    |
    */
    'model_permissions' => [
        'view_any',
        'view', 
        'create',
        'update',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
        'restore',
        'restore_any',
        'replicate',
        'reorder',
    ],

    /*
    |--------------------------------------------------------------------------
    | Route Permissions
    |--------------------------------------------------------------------------
    |
    | Settings for route-based permissions
    |
    */
    'route_permissions' => [
        'prefix' => 'access_',
        'exclude_patterns' => [
            'filament.*',
            'livewire.*',
            'debugbar.*',
            '_ignition.*',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Panel Integration
    |--------------------------------------------------------------------------
    |
    | Settings for FilamentPHP panel integration
    |
    */
    'panels' => [
        'admin' => [
            'enabled' => true,
            'navigation_group' => 'System',
            'navigation_sort' => 100,
        ],
    ],
];
