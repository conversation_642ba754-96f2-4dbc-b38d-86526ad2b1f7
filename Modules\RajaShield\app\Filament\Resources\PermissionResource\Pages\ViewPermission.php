<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;

class ViewPermission extends ViewRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit'),
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->requiresConfirmation()
                ->modalHeading('Hapus Permission')
                ->modalDescription('Apakah Anda yakin ingin menghapus permission ini? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Hapus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Permission: ' . $this->record->name;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Permission')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama Permission')
                                    ->weight('bold'),

                                TextEntry::make('guard_name')
                                    ->label('Guard Name')
                                    ->badge()
                                    ->color('gray'),

                                TextEntry::make('category')
                                    ->label('Kategori')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'user' => 'info',
                                        'role' => 'warning',
                                        'permission' => 'danger',
                                        'content' => 'success',
                                        'system' => 'primary',
                                        default => 'gray',
                                    })
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'user' => 'User Management',
                                        'role' => 'Role Management',
                                        'permission' => 'Permission Management',
                                        'content' => 'Content Management',
                                        'system' => 'System Settings',
                                        default => 'Other',
                                    }),

                                TextEntry::make('created_at')
                                    ->label('Dibuat')
                                    ->dateTime('d M Y H:i'),

                                TextEntry::make('updated_at')
                                    ->label('Diupdate')
                                    ->dateTime('d M Y H:i'),
                            ]),

                        TextEntry::make('description')
                            ->label('Deskripsi')
                            ->columnSpanFull()
                            ->placeholder('Tidak ada deskripsi'),
                    ]),

                Section::make('Roles dengan Permission Ini')
                    ->schema([
                        RepeatableEntry::make('roles')
                            ->label('Daftar Roles')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama Role')
                                    ->badge()
                                    ->color('success'),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
