<?php

namespace Modules\RajaShield\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RajaShieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create basic permissions
        $this->createBasicPermissions();
        
        // Create roles
        $this->createRoles();
        
        // Assign permissions to roles
        $this->assignPermissionsToRoles();
        
        // Create super admin user if not exists
        $this->createSuperAdminUser();
    }

    /**
     * Create basic permissions
     */
    protected function createBasicPermissions(): void
    {
        $permissions = [
            // Role management
            'view_any_role',
            'view_role',
            'create_role',
            'update_role',
            'delete_role',
            
            // Permission management
            'view_any_permission',
            'view_permission',
            'create_permission',
            'update_permission',
            'delete_permission',
            
            // User management
            'view_any_user',
            'view_user',
            'create_user',
            'update_user',
            'delete_user',
            
            // System access
            'access_admin_panel',
            'access_system_settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ], [
                'category' => $this->getPermissionCategory($permission),
                'description' => $this->getPermissionDescription($permission),
            ]);
        }
    }

    /**
     * Create roles
     */
    protected function createRoles(): void
    {
        $roles = [
            'super-admin' => 'Super Administrator with all permissions',
            'admin' => 'Administrator with most permissions',
            'editor' => 'Editor with content management permissions',
            'member' => 'Basic member with limited permissions',
        ];

        foreach ($roles as $roleName => $description) {
            Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web',
            ]);
        }
    }

    /**
     * Assign permissions to roles
     */
    protected function assignPermissionsToRoles(): void
    {
        // Super admin gets all permissions (handled by Gate::before in AppServiceProvider)
        
        // Admin role permissions
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminPermissions = [
                'view_any_user', 'view_user', 'create_user', 'update_user',
                'view_any_role', 'view_role', 'create_role', 'update_role',
                'view_any_permission', 'view_permission', 'create_permission', 'update_permission',
                'access_admin_panel', 'access_system_settings',
            ];
            $adminRole->syncPermissions($adminPermissions);
        }

        // Editor role permissions
        $editorRole = Role::where('name', 'editor')->first();
        if ($editorRole) {
            $editorPermissions = [
                'view_any_user', 'view_user',
                'access_admin_panel',
            ];
            $editorRole->syncPermissions($editorPermissions);
        }

        // Member role permissions (minimal)
        $memberRole = Role::where('name', 'member')->first();
        if ($memberRole) {
            $memberPermissions = [
                'view_user', // Can only view their own profile
            ];
            $memberRole->syncPermissions($memberPermissions);
        }
    }

    /**
     * Create super admin user if not exists
     */
    protected function createSuperAdminUser(): void
    {
        $superAdminEmail = '<EMAIL>';
        
        $user = User::where('email', $superAdminEmail)->first();
        
        if (!$user) {
            $user = User::create([
                'name' => 'Super Admin',
                'email' => $superAdminEmail,
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Assign super-admin role
        if (!$user->hasRole('super-admin')) {
            $user->assignRole('super-admin');
        }
    }

    /**
     * Get permission category based on permission name
     */
    protected function getPermissionCategory(string $permission): string
    {
        if (str_contains($permission, 'role')) {
            return 'role';
        }
        
        if (str_contains($permission, 'permission')) {
            return 'permission';
        }
        
        if (str_contains($permission, 'user')) {
            return 'user';
        }
        
        if (str_contains($permission, 'access')) {
            return 'system';
        }
        
        return 'other';
    }

    /**
     * Get permission description
     */
    protected function getPermissionDescription(string $permission): string
    {
        $descriptions = [
            'view_any_role' => 'View any role',
            'view_role' => 'View specific role',
            'create_role' => 'Create new role',
            'update_role' => 'Update existing role',
            'delete_role' => 'Delete role',
            
            'view_any_permission' => 'View any permission',
            'view_permission' => 'View specific permission',
            'create_permission' => 'Create new permission',
            'update_permission' => 'Update existing permission',
            'delete_permission' => 'Delete permission',
            
            'view_any_user' => 'View any user',
            'view_user' => 'View specific user',
            'create_user' => 'Create new user',
            'update_user' => 'Update existing user',
            'delete_user' => 'Delete user',
            
            'access_admin_panel' => 'Access admin panel',
            'access_system_settings' => 'Access system settings',
        ];

        return $descriptions[$permission] ?? ucfirst(str_replace('_', ' ', $permission));
    }
}
