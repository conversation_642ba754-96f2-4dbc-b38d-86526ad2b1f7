<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat'),
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->requiresConfirmation()
                ->modalHeading('Hapus Role')
                ->modalDescription('Apakah Anda yakin ingin menghapus role ini? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->visible(function ($record) {
                    // Prevent deletion of super-admin role
                    return $record->name !== 'super-admin';
                }),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Role: ' . $this->record->name;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Role berhasil diupdate';
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure guard_name is set
        if (empty($data['guard_name'])) {
            $data['guard_name'] = 'web';
        }

        return $data;
    }
}
