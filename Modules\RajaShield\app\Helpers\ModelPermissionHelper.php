<?php

namespace Modules\RajaShield\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;

class ModelPermissionHelper
{
    /**
     * Cache key for model permissions
     */
    const CACHE_KEY = 'rajashield_model_permissions';

    /**
     * Cache duration in seconds (10 minutes)
     */
    const CACHE_DURATION = 600;

    /**
     * Standard CRUD permissions for models
     */
    const STANDARD_PERMISSIONS = [
        'view_any',
        'view',
        'create',
        'update',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
        'restore',
        'restore_any',
        'replicate',
        'reorder',
    ];

    /**
     * Get all model permissions with caching
     */
    public static function getAllModelPermissions(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_DURATION, function () {
            return self::discoverModelPermissions();
        });
    }

    /**
     * Discover model permissions from app and modules
     */
    public static function discoverModelPermissions(): array
    {
        $permissions = [];

        // Discover from app/Models
        $appModels = self::discoverModelsInDirectory(app_path('Models'));
        
        // Discover from modules
        $moduleModels = self::discoverModelsInModules();

        $allModels = array_merge($appModels, $moduleModels);

        foreach ($allModels as $model) {
            $modelName = self::getModelNameFromClass($model);
            $permissions = array_merge($permissions, self::generatePermissionsForModel($modelName));
        }

        return $permissions;
    }

    /**
     * Discover models in a directory
     */
    protected static function discoverModelsInDirectory(string $directory): array
    {
        $models = [];

        if (!File::exists($directory)) {
            return $models;
        }

        $files = File::allFiles($directory);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = self::getClassNameFromFile($file->getPathname(), 'App\\Models\\');
                
                if ($className && class_exists($className)) {
                    $models[] = $className;
                }
            }
        }

        return $models;
    }

    /**
     * Discover models in modules
     */
    protected static function discoverModelsInModules(): array
    {
        $models = [];
        $modulesPath = base_path('Modules');

        if (!File::exists($modulesPath)) {
            return $models;
        }

        $moduleDirectories = File::directories($modulesPath);

        foreach ($moduleDirectories as $moduleDir) {
            $modelsPath = $moduleDir . '/app/Models';
            
            if (File::exists($modelsPath)) {
                $moduleName = basename($moduleDir);
                $moduleModels = self::discoverModelsInModuleDirectory($modelsPath, $moduleName);
                $models = array_merge($models, $moduleModels);
            }
        }

        return $models;
    }

    /**
     * Discover models in module directory
     */
    protected static function discoverModelsInModuleDirectory(string $directory, string $moduleName): array
    {
        $models = [];
        $files = File::allFiles($directory);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = self::getClassNameFromFile($file->getPathname(), "Modules\\{$moduleName}\\Models\\");
                
                if ($className && class_exists($className)) {
                    $models[] = $className;
                }
            }
        }

        return $models;
    }

    /**
     * Get class name from file
     */
    protected static function getClassNameFromFile(string $filePath, string $namespace): ?string
    {
        $relativePath = str_replace([dirname($filePath) . '/', '.php'], ['', ''], basename($filePath));
        return $namespace . $relativePath;
    }

    /**
     * Get model name from class
     */
    protected static function getModelNameFromClass(string $className): string
    {
        $parts = explode('\\', $className);
        $modelName = end($parts);
        
        return Str::snake($modelName);
    }

    /**
     * Generate permissions for a model
     */
    protected static function generatePermissionsForModel(string $modelName): array
    {
        $permissions = [];

        foreach (self::STANDARD_PERMISSIONS as $action) {
            $permissions[] = [
                'name' => "{$action}_{$modelName}",
                'guard_name' => 'web',
                'category' => 'model',
                'description' => "Permission to {$action} {$modelName}",
            ];
        }

        return $permissions;
    }

    /**
     * Sync model permissions to database
     */
    public static function syncModelPermissions(): int
    {
        $permissions = self::getAllModelPermissions();
        $created = 0;

        foreach ($permissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                [
                    'name' => $permissionData['name'],
                    'guard_name' => $permissionData['guard_name']
                ],
                $permissionData
            );

            if ($permission->wasRecentlyCreated) {
                $created++;
            }
        }

        return $created;
    }

    /**
     * Clear model permissions cache
     */
    public static function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }

    /**
     * Get permissions for specific model
     */
    public static function getPermissionsForModel(string $modelName): array
    {
        $allPermissions = self::getAllModelPermissions();
        
        return array_filter($allPermissions, function ($permission) use ($modelName) {
            return str_ends_with($permission['name'], "_{$modelName}");
        });
    }

    /**
     * Check if permission exists for model action
     */
    public static function hasPermissionForModel(string $action, string $modelName): bool
    {
        $permissionName = "{$action}_{$modelName}";
        
        return Permission::where('name', $permissionName)->exists();
    }

    /**
     * Create permission for model action if it doesn't exist
     */
    public static function ensurePermissionExists(string $action, string $modelName): Permission
    {
        $permissionName = "{$action}_{$modelName}";
        
        return Permission::firstOrCreate(
            [
                'name' => $permissionName,
                'guard_name' => 'web'
            ],
            [
                'category' => 'model',
                'description' => "Permission to {$action} {$modelName}",
            ]
        );
    }
}
