<?php

namespace Modules\RajaShield\Filament\Resources;

use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Modules\RajaShield\Filament\Resources\RoleResource\Pages;
use Modules\RajaShield\Helpers\SuperAdminHelper;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\CheckboxList;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationLabel = 'Role Management';

    protected static ?string $modelLabel = 'Role';

    protected static ?string $pluralModelLabel = 'Roles';

    protected static ?string $navigationGroup = 'System';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Role')
                    ->description('Kelola informasi dasar role')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Role')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('Contoh: admin, editor, member')
                            ->helperText('Nama role harus unik dan akan digunakan sebagai identifier'),

                        TextInput::make('guard_name')
                            ->label('Guard Name')
                            ->default('web')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Guard yang digunakan untuk role ini (default: web)'),
                    ])
                    ->columns(2),

                Section::make('Permissions')
                    ->description('Pilih permissions yang akan diberikan ke role ini')
                    ->schema([
                        CheckboxList::make('permissions')
                            ->label('Daftar Permissions')
                            ->relationship('permissions', 'name')
                            ->options(function () {
                                return Permission::all()->pluck('name', 'name')->toArray();
                            })
                            ->columns(3)
                            ->searchable()
                            ->bulkToggleable()
                            ->helperText('Pilih permissions yang akan diberikan ke role ini'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Role')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('gray'),

                TextColumn::make('permissions_count')
                    ->label('Jumlah Permissions')
                    ->counts('permissions')
                    ->badge()
                    ->color('success'),

                TextColumn::make('users_count')
                    ->label('Jumlah Users')
                    ->counts('users')
                    ->badge()
                    ->color('info'),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Diupdate')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),
            ])
            ->actions([
                ViewAction::make()
                    ->label('Lihat'),
                EditAction::make()
                    ->label('Edit'),
                DeleteAction::make()
                    ->label('Hapus')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Role')
                    ->modalDescription('Apakah Anda yakin ingin menghapus role ini? Tindakan ini tidak dapat dibatalkan.')
                    ->modalSubmitActionLabel('Ya, Hapus'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) || 
            $user->can('view_role')
        );
    }

    public static function canCreate(): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) || 
            $user->can('create_role')
        );
    }

    public static function canEdit($record): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) || 
            $user->can('update_role')
        );
    }

    public static function canDelete($record): bool
    {
        // Prevent deletion of super-admin role
        if ($record->name === 'super-admin') {
            return false;
        }

        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) || 
            $user->can('delete_role')
        );
    }

    public static function canViewAny(): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) || 
            $user->can('view_any_role')
        );
    }
}
