<?php

namespace Modules\RajaShield\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Permission;
use Modules\RajaShield\Helpers\ModelPermissionHelper;
use Modules\RajaShield\Helpers\SuperAdminHelper;

class SyncPermissionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rajashield:sync-permissions 
                            {--type=dual : Permission type to sync (route, model, dual)}
                            {--force : Force sync even if permissions exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync permissions from routes and models';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $force = $this->option('force');

        $this->info('Starting permission synchronization...');

        $totalCreated = 0;

        // Sync route permissions
        if (in_array($type, ['route', 'dual'])) {
            $this->info('Syncing route permissions...');
            $routeCreated = $this->syncRoutePermissions($force);
            $totalCreated += $routeCreated;
            $this->info("Created {$routeCreated} route permissions.");
        }

        // Sync model permissions
        if (in_array($type, ['model', 'dual'])) {
            $this->info('Syncing model permissions...');
            $modelCreated = $this->syncModelPermissions($force);
            $totalCreated += $modelCreated;
            $this->info("Created {$modelCreated} model permissions.");
        }

        // Ensure super admin role exists
        $this->info('Ensuring super admin role exists...');
        SuperAdminHelper::ensureSuperAdminRoleExists();

        $this->info("Permission synchronization completed. Total created: {$totalCreated}");

        return Command::SUCCESS;
    }

    /**
     * Sync route-based permissions
     */
    protected function syncRoutePermissions(bool $force = false): int
    {
        $created = 0;
        $routes = Route::getRoutes();
        $excludePatterns = config('rajashield.route_permissions.exclude_patterns', []);
        $prefix = config('rajashield.route_permissions.prefix', 'access_');

        foreach ($routes as $route) {
            $routeName = $route->getName();
            
            if (!$routeName) {
                continue;
            }

            // Skip excluded patterns
            $shouldSkip = false;
            foreach ($excludePatterns as $pattern) {
                if (fnmatch($pattern, $routeName)) {
                    $shouldSkip = true;
                    break;
                }
            }

            if ($shouldSkip) {
                continue;
            }

            $permissionName = $prefix . $routeName;

            // Check if permission already exists
            if (!$force && Permission::where('name', $permissionName)->exists()) {
                continue;
            }

            Permission::firstOrCreate(
                [
                    'name' => $permissionName,
                    'guard_name' => 'web'
                ],
                [
                    'category' => 'route',
                    'description' => "Access to route: {$routeName}",
                ]
            );

            $created++;
        }

        return $created;
    }

    /**
     * Sync model-based permissions
     */
    protected function syncModelPermissions(bool $force = false): int
    {
        // Clear cache to get fresh model list
        ModelPermissionHelper::clearCache();
        
        return ModelPermissionHelper::syncModelPermissions();
    }
}
