<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPermission extends EditRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat'),
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->requiresConfirmation()
                ->modalHeading('Hapus Permission')
                ->modalDescription('Apakah Anda yakin ingin menghapus permission ini? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Hapus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Permission: ' . $this->record->name;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Permission berhasil diupdate';
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure guard_name is set
        if (empty($data['guard_name'])) {
            $data['guard_name'] = 'web';
        }

        // Set default category if not provided
        if (empty($data['category'])) {
            $data['category'] = 'other';
        }

        return $data;
    }
}
