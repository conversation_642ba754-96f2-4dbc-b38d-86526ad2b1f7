<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;

class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit'),
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->requiresConfirmation()
                ->modalHeading('Hapus Role')
                ->modalDescription('Apakah Anda yakin ingin menghapus role ini? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->visible(function ($record) {
                    // Prevent deletion of super-admin role
                    return $record->name !== 'super-admin';
                }),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Role: ' . $this->record->name;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Role')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama Role')
                                    ->weight('bold'),

                                TextEntry::make('guard_name')
                                    ->label('Guard Name')
                                    ->badge()
                                    ->color('gray'),

                                TextEntry::make('created_at')
                                    ->label('Dibuat')
                                    ->dateTime('d M Y H:i'),

                                TextEntry::make('updated_at')
                                    ->label('Diupdate')
                                    ->dateTime('d M Y H:i'),
                            ]),
                    ]),

                Section::make('Permissions')
                    ->schema([
                        RepeatableEntry::make('permissions')
                            ->label('Daftar Permissions')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Permission Name')
                                    ->badge()
                                    ->color('success'),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ]),

                Section::make('Users dengan Role Ini')
                    ->schema([
                        RepeatableEntry::make('users')
                            ->label('Daftar Users')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama User'),
                                TextEntry::make('email')
                                    ->label('Email'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
