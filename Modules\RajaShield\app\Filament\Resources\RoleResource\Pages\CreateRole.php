<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Resources\Pages\CreateRecord;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    public function getTitle(): string
    {
        return 'Tambah Role Baru';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Role berhasil dibuat';
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure guard_name is set
        if (empty($data['guard_name'])) {
            $data['guard_name'] = 'web';
        }

        return $data;
    }
}
