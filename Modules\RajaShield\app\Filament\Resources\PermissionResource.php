<?php

namespace Modules\RajaShield\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Modules\RajaShield\Filament\Resources\PermissionResource\Pages;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\HeaderAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Filament\Notifications\Notification;

class PermissionResource extends Resource
{
    protected static ?string $model = Permission::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';

    protected static ?string $navigationLabel = 'Permission Management';

    protected static ?string $modelLabel = 'Permission';

    protected static ?string $pluralModelLabel = 'Permissions';

    protected static ?string $navigationGroup = 'System';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Permission')
                    ->description('Kelola informasi dasar permission')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Permission')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('Contoh: view_users, create_posts, edit_settings')
                            ->helperText('Nama permission harus unik dan menggunakan format: action_resource'),

                        TextInput::make('guard_name')
                            ->label('Guard Name')
                            ->default('web')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Guard yang digunakan untuk permission ini (default: web)'),

                        Select::make('category')
                            ->label('Kategori')
                            ->options([
                                'user' => 'User Management',
                                'role' => 'Role Management',
                                'permission' => 'Permission Management',
                                'content' => 'Content Management',
                                'system' => 'System Settings',
                                'other' => 'Other',
                            ])
                            ->default('other')
                            ->helperText('Kategori untuk mengelompokkan permissions'),

                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->maxLength(500)
                            ->placeholder('Deskripsi singkat tentang permission ini')
                            ->helperText('Deskripsi opsional untuk menjelaskan fungsi permission'),
                    ])
                    ->columns(2),

                Section::make('Roles')
                    ->description('Pilih roles yang akan memiliki permission ini')
                    ->schema([
                        CheckboxList::make('roles')
                            ->label('Daftar Roles')
                            ->relationship('roles', 'name')
                            ->options(Role::all()->pluck('name', 'name'))
                            ->columns(3)
                            ->searchable()
                            ->bulkToggleable()
                            ->helperText('Pilih roles yang akan memiliki permission ini'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Permission')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('gray'),

                TextColumn::make('category')
                    ->label('Kategori')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'user' => 'info',
                        'role' => 'warning',
                        'permission' => 'danger',
                        'content' => 'success',
                        'system' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'user' => 'User Management',
                        'role' => 'Role Management',
                        'permission' => 'Permission Management',
                        'content' => 'Content Management',
                        'system' => 'System Settings',
                        default => 'Other',
                    }),

                TextColumn::make('roles_count')
                    ->label('Jumlah Roles')
                    ->counts('roles')
                    ->badge()
                    ->color('success'),

                TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Diupdate')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),

                Tables\Filters\SelectFilter::make('category')
                    ->label('Kategori')
                    ->options([
                        'user' => 'User Management',
                        'role' => 'Role Management',
                        'permission' => 'Permission Management',
                        'content' => 'Content Management',
                        'system' => 'System Settings',
                        'other' => 'Other',
                    ]),
            ])
            ->headerActions([
                Action::make('sync_permissions')
                    ->label('Sync Permissions')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->action(function () {
                        static::syncPermissions();
                        
                        Notification::make()
                            ->title('Permissions berhasil disinkronisasi')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Sync Permissions')
                    ->modalDescription('Ini akan melakukan auto-discovery dan membuat permissions baru berdasarkan routes dan resources yang ada. Lanjutkan?')
                    ->modalSubmitActionLabel('Ya, Sync'),
            ])
            ->actions([
                ViewAction::make()
                    ->label('Lihat'),
                EditAction::make()
                    ->label('Edit'),
                DeleteAction::make()
                    ->label('Hapus')
                    ->requiresConfirmation()
                    ->modalHeading('Hapus Permission')
                    ->modalDescription('Apakah Anda yakin ingin menghapus permission ini? Tindakan ini tidak dapat dibatalkan.')
                    ->modalSubmitActionLabel('Ya, Hapus'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissions::route('/'),
            'create' => Pages\CreatePermission::route('/create'),
            'view' => Pages\ViewPermission::route('/{record}'),
            'edit' => Pages\EditPermission::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * Sync permissions from routes and resources
     */
    public static function syncPermissions(): void
    {
        $permissions = [];

        // Get all routes and create permissions
        $routes = Route::getRoutes();
        foreach ($routes as $route) {
            $routeName = $route->getName();
            if ($routeName && !str_starts_with($routeName, 'filament.')) {
                $permissions[] = [
                    'name' => "access_{$routeName}",
                    'guard_name' => 'web',
                    'category' => 'route',
                ];
            }
        }

        // Add standard CRUD permissions for common resources
        $resources = ['user', 'role', 'permission', 'post', 'page', 'setting'];
        $actions = ['view', 'view_any', 'create', 'update', 'delete', 'delete_any'];

        foreach ($resources as $resource) {
            foreach ($actions as $action) {
                $permissions[] = [
                    'name' => "{$action}_{$resource}",
                    'guard_name' => 'web',
                    'category' => $resource,
                ];
            }
        }

        // Create permissions if they don't exist
        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name'], 'guard_name' => $permissionData['guard_name']],
                $permissionData
            );
        }
    }

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) ||
            $user->can('view_permission')
        );
    }

    public static function canCreate(): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) ||
            $user->can('create_permission')
        );
    }

    public static function canEdit($record): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) ||
            $user->can('update_permission')
        );
    }

    public static function canDelete($record): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) ||
            $user->can('delete_permission')
        );
    }

    public static function canViewAny(): bool
    {
        $user = Auth::user();
        return $user && (
            SuperAdminHelper::isSuperAdmin($user) ||
            $user->can('view_any_permission')
        );
    }
}
