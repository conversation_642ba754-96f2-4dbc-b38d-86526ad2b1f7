<?php

namespace Modules\RajaShield\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpFoundation\Response;
use Modules\RajaShield\Helpers\SuperAdminHelper;

class RajaShieldMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission = null): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            abort(401, 'Unauthorized');
        }

        $user = Auth::user();

        // Super admin bypass - check if user has super-admin role
        if (SuperAdminHelper::isSuperAdmin($user)) {
            return $next($request);
        }

        // If no specific permission is provided, try to determine from route
        if (!$permission) {
            $permission = $this->getPermissionFromRoute($request);
        }

        // Check if permission is required and user has it
        if ($permission && !$user->can($permission)) {
            abort(403, 'Forbidden - You do not have permission to access this resource');
        }

        return $next($request);
    }

    /**
     * Get permission name from current route
     */
    protected function getPermissionFromRoute(Request $request): ?string
    {
        $route = $request->route();
        
        if (!$route) {
            return null;
        }

        $routeName = $route->getName();
        
        if (!$routeName) {
            return null;
        }

        // Skip Filament internal routes
        if (str_starts_with($routeName, 'filament.')) {
            return null;
        }

        // Convert route name to permission name
        // Example: admin.users.index -> access_admin_users_index
        return "access_{$routeName}";
    }

    /**
     * Get permission for Filament resource action
     */
    protected function getFilamentPermission(Request $request): ?string
    {
        $route = $request->route();
        
        if (!$route) {
            return null;
        }

        // Get resource and action from route parameters
        $resource = $route->parameter('resource');
        $action = $this->getActionFromRoute($route);

        if (!$resource || !$action) {
            return null;
        }

        // Convert to permission format
        // Example: users + index -> view_any_user
        return $this->convertToPermissionName($action, $resource);
    }

    /**
     * Get action from route
     */
    protected function getActionFromRoute($route): ?string
    {
        $routeName = $route->getName();
        
        if (!$routeName) {
            return null;
        }

        // Extract action from route name
        // Example: filament.admin.resources.users.index -> index
        $parts = explode('.', $routeName);
        return end($parts);
    }

    /**
     * Convert action and resource to permission name
     */
    protected function convertToPermissionName(string $action, string $resource): string
    {
        // Convert plural resource to singular
        $singular = str_singular($resource);

        // Map Filament actions to permission actions
        $actionMap = [
            'index' => 'view_any',
            'create' => 'create',
            'store' => 'create',
            'show' => 'view',
            'edit' => 'update',
            'update' => 'update',
            'destroy' => 'delete',
        ];

        $permissionAction = $actionMap[$action] ?? $action;

        return "{$permissionAction}_{$singular}";
    }
}
