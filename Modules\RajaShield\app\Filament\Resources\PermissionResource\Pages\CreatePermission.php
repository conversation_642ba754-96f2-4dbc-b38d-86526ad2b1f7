<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Resources\Pages\CreateRecord;

class CreatePermission extends CreateRecord
{
    protected static string $resource = PermissionResource::class;

    public function getTitle(): string
    {
        return 'Tambah Permission Baru';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Permission berhasil dibuat';
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure guard_name is set
        if (empty($data['guard_name'])) {
            $data['guard_name'] = 'web';
        }

        // Set default category if not provided
        if (empty($data['category'])) {
            $data['category'] = 'other';
        }

        return $data;
    }
}
