<?php

namespace Modules\RajaShield\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Contracts\Auth\Authenticatable;

class SuperAdminHelper
{
    /**
     * Cache key prefix for super admin status
     */
    const CACHE_PREFIX = 'rajashield_super_admin_';

    /**
     * Cache duration in seconds (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Super admin role name
     */
    const SUPER_ADMIN_ROLE = 'super-admin';

    /**
     * Check if user is super admin with caching
     */
    public static function isSuperAdmin(?Authenticatable $user = null): bool
    {
        if (!$user) {
            return false;
        }

        $cacheKey = self::CACHE_PREFIX . $user->getAuthIdentifier();

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($user) {
            return $user->hasRole(self::SUPER_ADMIN_ROLE);
        });
    }

    /**
     * Clear super admin cache for user
     */
    public static function clearCache(?Authenticatable $user = null): void
    {
        if (!$user) {
            return;
        }

        $cacheKey = self::CACHE_PREFIX . $user->getAuthIdentifier();
        Cache::forget($cacheKey);
    }

    /**
     * Clear all super admin caches
     */
    public static function clearAllCaches(): void
    {
        // Get all cache keys with our prefix and clear them
        $keys = Cache::getRedis()->keys(self::CACHE_PREFIX . '*');
        
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }

    /**
     * Check if user has super admin role without caching
     */
    public static function isSuperAdminDirect(?Authenticatable $user = null): bool
    {
        if (!$user) {
            return false;
        }

        return $user->hasRole(self::SUPER_ADMIN_ROLE);
    }

    /**
     * Get super admin role name
     */
    public static function getSuperAdminRole(): string
    {
        return self::SUPER_ADMIN_ROLE;
    }

    /**
     * Create super admin role if it doesn't exist
     */
    public static function ensureSuperAdminRoleExists(): void
    {
        $roleClass = config('permission.models.role');
        
        $roleClass::firstOrCreate([
            'name' => self::SUPER_ADMIN_ROLE,
            'guard_name' => 'web',
        ]);
    }

    /**
     * Assign super admin role to user
     */
    public static function assignSuperAdminRole(Authenticatable $user): void
    {
        self::ensureSuperAdminRoleExists();
        
        if (!$user->hasRole(self::SUPER_ADMIN_ROLE)) {
            $user->assignRole(self::SUPER_ADMIN_ROLE);
            self::clearCache($user);
        }
    }

    /**
     * Remove super admin role from user
     */
    public static function removeSuperAdminRole(Authenticatable $user): void
    {
        if ($user->hasRole(self::SUPER_ADMIN_ROLE)) {
            $user->removeRole(self::SUPER_ADMIN_ROLE);
            self::clearCache($user);
        }
    }

    /**
     * Check if current authenticated user is super admin
     */
    public static function isCurrentUserSuperAdmin(): bool
    {
        return self::isSuperAdmin(auth()->user());
    }
}
